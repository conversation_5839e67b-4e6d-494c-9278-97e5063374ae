export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  grade: string;
  section: string;
  dateOfBirth?: string;
  parentName?: string;
  parentEmail?: string;
  parentPhone?: string;
  profileImage?: string;
  enrollmentDate: string;
  status: 'active' | 'inactive' | 'graduated';
  subjects: string[];
  notes?: string;
  // Academic Information
  currentGPA?: number;
  attendance?: number;
  behaviorRating?: number;
  // Progress Tracking
  completedLessons: string[];
  completedExercises: string[];
  achievements: string[];
  lastActivity?: string;
}

export interface StudentProgress {
  studentId: string;
  subject: string;
  lessonsCompleted: number;
  totalLessons: number;
  exercisesCompleted: number;
  totalExercises: number;
  averageScore: number;
  lastUpdated: string;
  weakAreas: string[];
  strongAreas: string[];
}

export interface StudentGrade {
  id: string;
  studentId: string;
  subject: string;
  assignmentName: string;
  score: number;
  maxScore: number;
  percentage: number;
  gradedDate: string;
  feedback?: string;
  category: 'homework' | 'quiz' | 'test' | 'project' | 'participation';
}

export interface StudentAttendance {
  id: string;
  studentId: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
}

export type StudentSortField = 'firstName' | 'lastName' | 'grade' | 'enrollmentDate' | 'lastActivity';
export type SortDirection = 'asc' | 'desc';

export interface StudentFilters {
  grade?: string;
  section?: string;
  status?: Student['status'];
  subject?: string;
  search?: string;
}
