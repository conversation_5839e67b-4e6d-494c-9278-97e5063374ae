import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { <PERSON><PERSON>, Card } from '../components';
import { Colors, Typography, Spacing, Layout, BorderRadius } from '../constants/theme';

const { width } = Dimensions.get('window');

type TeacherDashboardScreenProps = NativeStackScreenProps<RootStackParamList, 'TeacherDashboard'>;

interface QuickStat {
  label: string;
  value: string;
  icon: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  color: string;
}

interface DashboardCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  onPress: () => void;
  badge?: string;
}

interface RecentActivity {
  id: string;
  type: 'student' | 'exercise' | 'report' | 'assignment';
  title: string;
  description: string;
  time: string;
  icon: string;
  priority: 'high' | 'medium' | 'low';
}

const TeacherDashboardScreen = ({ navigation }: TeacherDashboardScreenProps) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'semester'>('week');

  const handleManageStudents = () => {
    console.log('Manage Students pressed');
  };

  const handleViewReports = () => {
    console.log('View Reports pressed');
  };

  const handleManageContent = () => {
    navigation.navigate('LessonList');
  };

  const handleManageExercises = () => {
    navigation.navigate('ExerciseList');
  };

  const handleCreateAssignment = () => {
    console.log('Create Assignment pressed');
  };

  const handleViewAnalytics = () => {
    console.log('View Analytics pressed');
  };

  const quickStats: QuickStat[] = [
    { 
      label: 'Active Students', 
      value: '24', 
      icon: '👥', 
      trend: 'up', 
      trendValue: '+2',
      color: Colors.teacher.primary 
    },
    {
      label: 'Class Average Score',
      value: '87%',
      icon: '📊',
      trend: 'up',
      trendValue: '+5%',
      color: Colors.teacher.success
    },
    {
      label: 'Need Grading',
      value: '12',
      icon: '📝',
      trend: 'down',
      trendValue: '-3',
      color: Colors.teacher.warning
    },
    {
      label: 'Homework Done',
      value: '92%',
      icon: '✅',
      trend: 'stable',
      trendValue: '0%',
      color: Colors.teacher.info
    },
  ];

  const dashboardCards: DashboardCard[] = [
    {
      id: '1',
      title: 'Student Management',
      description: 'Manage student roster, grades, and profiles',
      icon: '👥',
      color: Colors.teacher.primary,
      onPress: handleManageStudents,
      badge: '24 students',
    },
    {
      id: '2',
      title: 'Content Library',
      description: 'Create and organize lessons and materials',
      icon: '📚',
      color: Colors.teacher.success,
      onPress: handleManageContent,
      badge: '8 lessons',
    },
    {
      id: '3',
      title: 'Exercise Builder',
      description: 'Design interactive exercises and assessments',
      icon: '✏️',
      color: Colors.teacher.warning,
      onPress: handleManageExercises,
      badge: '15 exercises',
    },
    {
      id: '4',
      title: 'Analytics & Reports',
      description: 'Track performance and generate insights',
      icon: '📈',
      color: Colors.teacher.info,
      onPress: handleViewReports,
      badge: 'New data',
    },
  ];

  const quickActions = [
    { title: 'Create Assignment', icon: '📋', onPress: handleCreateAssignment, color: Colors.teacher.accent },
    { title: 'View Analytics', icon: '📊', onPress: handleViewAnalytics, color: Colors.teacher.primary },
    { title: 'Grade Submissions', icon: '✅', onPress: handleViewReports, color: Colors.teacher.success },
  ];

  const recentActivities: RecentActivity[] = [
    {
      id: '1',
      type: 'student',
      title: 'Emma completed Math Quiz',
      description: 'Scored 95% on Multiplication Tables',
      time: '2 hours ago',
      icon: '🎯',
      priority: 'high',
    },
    {
      id: '2',
      type: 'assignment',
      title: 'New assignment submissions',
      description: '8 students submitted Reading Comprehension',
      time: '4 hours ago',
      icon: '📝',
      priority: 'medium',
    },
    {
      id: '3',
      type: 'exercise',
      title: 'Exercise performance alert',
      description: '3 students struggling with Fractions',
      time: '6 hours ago',
      icon: '⚠️',
      priority: 'high',
    },
    {
      id: '4',
      type: 'report',
      title: 'Weekly progress report ready',
      description: 'Class average improved by 12%',
      time: '1 day ago',
      icon: '📊',
      priority: 'low',
    },
  ];

  const renderProgressBar = (percentage: number, color: string) => (
    <View style={styles.progressBarContainer}>
      <View style={[styles.progressBar, { backgroundColor: color + '20' }]}>
        <View 
          style={[
            styles.progressFill, 
            { width: `${percentage}%`, backgroundColor: color }
          ]} 
        />
      </View>
      <Text style={[styles.progressText, { color }]}>{percentage}%</Text>
    </View>
  );

  const renderTrendIndicator = (trend: 'up' | 'down' | 'stable', value: string) => {
    const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';
    const trendColor = trend === 'up' ? Colors.teacher.success : trend === 'down' ? Colors.error[500] : Colors.text.secondary;
    
    return (
      <View style={styles.trendContainer}>
        <Text style={[styles.trendIcon, { color: trendColor }]}>{trendIcon}</Text>
        <Text style={[styles.trendValue, { color: trendColor }]}>{value}</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Enhanced Header */}
        <View style={styles.header}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>Good morning,</Text>
            <Text style={styles.teacherName}>Ms. Johnson</Text>
            <Text style={styles.classInfo}>Grade 3A • 24 Students</Text>
          </View>
          <TouchableOpacity style={styles.avatarContainer}>
            <Text style={styles.avatarText}>👩‍🏫</Text>
            <View style={styles.statusIndicator} />
          </TouchableOpacity>
        </View>

        {/* Timeframe Selector */}
        <View style={styles.timeframeSelector}>
          {(['week', 'month', 'semester'] as const).map((timeframe) => (
            <TouchableOpacity
              key={timeframe}
              style={[
                styles.timeframeButton,
                selectedTimeframe === timeframe && styles.timeframeButtonActive
              ]}
              onPress={() => setSelectedTimeframe(timeframe)}
            >
              <Text style={[
                styles.timeframeText,
                selectedTimeframe === timeframe && styles.timeframeTextActive
              ]}>
                {timeframe.charAt(0).toUpperCase() + timeframe.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Enhanced Quick Stats */}
        <Card variant="teacher" style={styles.statsCard}>
          <Text style={styles.sectionTitle}>Performance Overview</Text>
          <View style={styles.statsGrid}>
            {quickStats.map((stat, index) => (
              <View key={index} style={styles.statItem}>
                <View style={styles.statHeader}>
                  <Text style={styles.statIcon}>{stat.icon}</Text>
                  {stat.trend && renderTrendIndicator(stat.trend, stat.trendValue!)}
                </View>
                <Text style={[styles.statValue, { color: stat.color }]}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
                {stat.label.includes('Performance') || stat.label.includes('Rate') ? 
                  renderProgressBar(parseInt(stat.value), stat.color) : null
                }
              </View>
            ))}
          </View>
        </Card>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickActionsScroll}>
            {quickActions.map((action, index) => (
              <TouchableOpacity key={index} style={styles.quickActionItem} onPress={action.onPress}>
                <View style={[styles.quickActionIcon, { backgroundColor: action.color + '20' }]}>
                  <Text style={styles.quickActionIconText}>{action.icon}</Text>
                </View>
                <Text style={styles.quickActionTitle}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Enhanced Main Actions */}
        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Main Features</Text>
          <View style={styles.cardsGrid}>
            {dashboardCards.map((card) => (
              <TouchableOpacity key={card.id} onPress={card.onPress} style={styles.actionCard}>
                <Card variant="elevated" style={[styles.cardContent, { borderLeftColor: card.color }]}>
                  <View style={styles.cardHeader}>
                    <View style={[styles.iconContainer, { backgroundColor: card.color + '15' }]}>
                      <Text style={styles.cardIcon}>{card.icon}</Text>
                    </View>
                    {card.badge && (
                      <View style={[styles.badge, { backgroundColor: card.color }]}>
                        <Text style={styles.badgeText}>{card.badge}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.cardTitle}>{card.title}</Text>
                  <Text style={styles.cardDescription}>{card.description}</Text>
                  <Button
                    title="Open"
                    onPress={card.onPress}
                    variant="outline"
                    size="sm"
                    style={[styles.cardButton, { borderColor: card.color }]}
                    textStyle={{ color: card.color }}
                  />
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Enhanced Recent Activity */}
        <Card variant="outlined" style={styles.activityCard}>
          <View style={styles.activityHeader}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.activityList}>
            {recentActivities.map((activity) => (
              <TouchableOpacity key={activity.id} style={styles.activityItem}>
                <View style={[styles.activityIconContainer, {
                  backgroundColor: activity.priority === 'high' ? Colors.error[500] + '20' :
                                 activity.priority === 'medium' ? Colors.teacher.warning + '20' :
                                 Colors.teacher.success + '20'
                }]}>
                  <Text style={styles.activityIcon}>{activity.icon}</Text>
                </View>
                <View style={styles.activityContent}>
                  <View style={styles.activityTitleRow}>
                    <Text style={styles.activityTitle}>{activity.title}</Text>
                    {activity.priority === 'high' && (
                      <View style={styles.priorityIndicator}>
                        <Text style={styles.priorityText}>!</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.activityDescription}>{activity.description}</Text>
                  <Text style={styles.activityTime}>{activity.time}</Text>
                </View>
                <Text style={styles.activityArrow}>›</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Class Performance Summary */}
        <Card variant="teacher" style={styles.performanceCard}>
          <Text style={styles.sectionTitle}>Class Performance Summary</Text>
          <View style={styles.performanceGrid}>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Math</Text>
              {renderProgressBar(89, Colors.teacher.primary)}
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Reading</Text>
              {renderProgressBar(94, Colors.teacher.success)}
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Science</Text>
              {renderProgressBar(76, Colors.teacher.warning)}
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Writing</Text>
              {renderProgressBar(82, Colors.teacher.info)}
            </View>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  scrollContent: {
    padding: Layout.screenPadding,
    paddingBottom: Spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    paddingTop: Spacing.sm,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  teacherName: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
  },
  classInfo: {
    fontSize: Typography.fontSize.sm,
    color: Colors.teacher.primary,
    fontWeight: '600' as const,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.teacher.primary,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    elevation: 3,
    shadowColor: Colors.teacher.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  avatarText: {
    fontSize: 28,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.teacher.success,
    borderWidth: 2,
    borderColor: Colors.background.primary,
  },
  timeframeSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
    marginBottom: Spacing.lg,
  },
  timeframeButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
  },
  timeframeButtonActive: {
    backgroundColor: Colors.teacher.primary,
  },
  timeframeText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600' as const,
    color: Colors.text.secondary,
  },
  timeframeTextActive: {
    color: Colors.background.primary,
  },
  statsCard: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: (width - Layout.screenPadding * 2 - Spacing.lg * 3) / 2,
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.md,
    elevation: 1,
    shadowColor: Colors.neutral[400],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  statIcon: {
    fontSize: 24,
  },
  statValue: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    marginBottom: Spacing.xs / 2,
  },
  statLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    fontWeight: '600' as const,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendIcon: {
    fontSize: 12,
    marginRight: Spacing.xs / 2,
  },
  trendValue: {
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    marginRight: Spacing.sm,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
    minWidth: 30,
  },
  quickActionsSection: {
    marginBottom: Spacing.lg,
  },
  quickActionsScroll: {
    marginHorizontal: -Spacing.xs,
  },
  quickActionItem: {
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
    minWidth: 80,
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.sm,
  },
  quickActionIconText: {
    fontSize: 24,
  },
  quickActionTitle: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.primary,
    textAlign: 'center',
    fontWeight: '600' as const,
  },
  actionsSection: {
    marginBottom: Spacing.lg,
  },
  cardsGrid: {
    gap: Spacing.md,
  },
  actionCard: {
    marginBottom: Spacing.sm,
  },
  cardContent: {
    borderLeftWidth: 4,
    position: 'relative',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardIcon: {
    fontSize: 28,
  },
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.full,
  },
  badgeText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.background.primary,
    fontWeight: '600' as const,
  },
  cardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  cardButton: {
    alignSelf: 'flex-start',
  },
  activityCard: {
    marginBottom: Spacing.lg,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  viewAllButton: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.teacher.primary,
    fontWeight: '600' as const,
  },
  activityList: {
    gap: Spacing.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  activityIconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },
  activityIcon: {
    fontSize: 18,
  },
  activityContent: {
    flex: 1,
  },
  activityTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs / 2,
  },
  activityTitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    fontWeight: '600' as const,
    flex: 1,
  },
  priorityIndicator: {
    width: 20,
    height: 20,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.error[500],
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.sm,
  },
  priorityText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.background.primary,
    fontWeight: '700' as const,
  },
  activityDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs / 2,
  },
  activityTime: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.tertiary,
  },
  activityArrow: {
    fontSize: 20,
    color: Colors.text.tertiary,
    marginLeft: Spacing.sm,
  },
  performanceCard: {
    marginBottom: Spacing.lg,
  },
  performanceGrid: {
    gap: Spacing.md,
  },
  performanceItem: {
    marginBottom: Spacing.sm,
  },
  performanceLabel: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    fontWeight: '600' as const,
    marginBottom: Spacing.sm,
  },
});

export default TeacherDashboardScreen;
