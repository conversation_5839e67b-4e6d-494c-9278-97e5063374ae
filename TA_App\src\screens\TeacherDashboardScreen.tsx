import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { <PERSON>ton, Card } from '../components';
import { Colors, Typography, Spacing, Layout, BorderRadius } from '../constants/theme';
// import { StudentService } from '../services/StudentService';
// import { initializeSampleData } from '../utils/sampleData';

const { width } = Dimensions.get('window');

type TeacherDashboardScreenProps = NativeStackScreenProps<RootStackParamList, 'TeacherDashboard'>;

interface QuickStat {
  label: string;
  value: string;
  icon: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  color: string;
}

interface DashboardCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  onPress: () => void;
  badge?: string;
}

interface RecentActivity {
  id: string;
  type: 'student' | 'exercise' | 'report' | 'assignment';
  title: string;
  description: string;
  time: string;
  icon: string;
  priority: 'high' | 'medium' | 'low';
}

interface TeachingSession {
  id: string;
  time: string;
  endTime: string;
  subject: string;
  studentGroup: string;
  topic: string;
  notes?: string;
  status: 'current' | 'next' | 'upcoming' | 'completed';
  hasAnnouncement?: boolean;
}

const TeacherDashboardScreen = ({ navigation }: TeacherDashboardScreenProps) => {
  const [studentCount, setStudentCount] = useState(24);

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    // Initialize sample data if needed
    // await initializeSampleData();

    // Load current student count
    // const stats = await StudentService.getStudentStats();
    // setStudentCount(stats.activeStudents);
  };

  const handleManageStudents = () => {
    // navigation.navigate('StudentList');
    console.log('Student management temporarily disabled');
  };

  const handleViewReports = () => {
    console.log('View Reports pressed');
  };

  const handleManageContent = () => {
    navigation.navigate('LessonList');
  };

  const handleManageExercises = () => {
    navigation.navigate('ExerciseList');
  };

  const handleCreateAssignment = () => {
    console.log('Create Assignment pressed');
  };

  const handleViewAnalytics = () => {
    console.log('View Analytics pressed');
  };

  // Remove performance stats for now

  const todaySchedule: TeachingSession[] = [
    {
      id: '1',
      time: '09:00',
      endTime: '10:00',
      subject: 'Mathematics',
      studentGroup: 'Grade 3A',
      topic: 'Fractions & Decimals',
      notes: 'Review homework, introduce new concepts',
      status: 'completed',
    },
    {
      id: '2',
      time: '10:30',
      endTime: '11:30',
      subject: 'Science',
      studentGroup: 'Grade 3B',
      topic: 'Plant Life Cycles',
      notes: 'Hands-on experiment with seeds',
      status: 'current',
      hasAnnouncement: true,
    },
    {
      id: '3',
      time: '13:00',
      endTime: '14:00',
      subject: 'English',
      studentGroup: 'Grade 3A',
      topic: 'Creative Writing',
      notes: 'Story composition exercise',
      status: 'next',
    },
    {
      id: '4',
      time: '14:30',
      endTime: '15:30',
      subject: 'Mathematics',
      studentGroup: 'Grade 3C',
      topic: 'Geometry Basics',
      notes: 'Shapes and angles introduction',
      status: 'upcoming',
    },
  ];

  const dashboardCards: DashboardCard[] = [
    {
      id: '1',
      title: 'Content Library',
      description: 'Manage lessons, materials & weekly teaching schedule',
      icon: '📚',
      color: Colors.teacher.primary,
      onPress: handleManageContent,
      badge: 'Schedule Ready',
    },
    {
      id: '2',
      title: 'Exercise Builder',
      description: 'Create exercises & link to weekly schedule',
      icon: '🔧',
      color: Colors.teacher.accent,
      onPress: handleManageExercises,
      badge: '5 Templates',
    },
    {
      id: '3',
      title: 'Student Management',
      description: 'Manage student roster, grades, and profiles',
      icon: '👥',
      color: Colors.teacher.info,
      onPress: handleManageStudents,
      badge: `${studentCount} Active`,
    },
    {
      id: '4',
      title: 'Teacher Performance',
      description: 'View teaching analytics and metrics',
      icon: '📊',
      color: Colors.teacher.success,
      onPress: handleViewAnalytics,
      badge: 'Updated',
    },
  ];

  const quickActionCards = [
    {
      id: '1',
      title: 'Create Assignment',
      subtitle: 'New homework or test',
      icon: '📝',
      onPress: handleCreateAssignment,
      color: Colors.teacher.accent,
      badge: null
    },
    {
      id: '2',
      title: 'Need Grading',
      subtitle: '12 submissions pending review',
      icon: '📋',
      onPress: () => console.log('Need Grading'),
      color: Colors.teacher.warning,
      badge: '12'
    },
    {
      id: '3',
      title: 'Weekly Schedule',
      subtitle: 'Plan your teaching week',
      icon: '📅',
      onPress: () => console.log('Weekly Schedule'),
      color: Colors.teacher.primary,
      badge: null
    },
    {
      id: '4',
      title: 'Send Announcement',
      subtitle: 'Message to students/parents',
      icon: '📢',
      onPress: () => console.log('Send Announcement'),
      color: Colors.teacher.info,
      badge: '!'
    },
  ];

  // Remove recent activities section

  const renderProgressBar = (percentage: number, color: string) => (
    <View style={styles.progressBarContainer}>
      <View style={[styles.progressBar, { backgroundColor: color + '20' }]}>
        <View
          style={[
            styles.progressFill,
            { width: `${percentage}%`, backgroundColor: color }
          ]}
        />
      </View>
      <Text style={[styles.progressText, { color }]}>{percentage}%</Text>
    </View>
  );

  const renderTeachingSession = (session: TeachingSession) => {
    const getStatusColor = () => {
      switch (session.status) {
        case 'current': return Colors.teacher.success;
        case 'next': return Colors.teacher.primary;
        case 'upcoming': return Colors.text.secondary;
        case 'completed': return Colors.neutral[400];
        default: return Colors.text.secondary;
      }
    };

    const getStatusIcon = () => {
      switch (session.status) {
        case 'current': return '🟢';
        case 'next': return '🔵';
        case 'upcoming': return '⏰';
        case 'completed': return '✅';
        default: return '⏰';
      }
    };

    return (
      <View key={session.id} style={[styles.scheduleItem, { borderLeftColor: getStatusColor() }]}>
        <View style={styles.scheduleHeader}>
          <View style={styles.scheduleTime}>
            <Text style={styles.scheduleTimeText}>{session.time}-{session.endTime}</Text>
            <Text style={styles.scheduleStatus}>{getStatusIcon()} {session.status.toUpperCase()}</Text>
          </View>
          {session.hasAnnouncement && (
            <View style={styles.announcementBadge}>
              <Text style={styles.announcementText}>📢</Text>
            </View>
          )}
        </View>
        <Text style={styles.scheduleSubject}>{session.subject}</Text>
        <Text style={styles.scheduleGroup}>{session.studentGroup}</Text>
        <Text style={styles.scheduleTopic}>{session.topic}</Text>
        {session.notes && (
          <Text style={styles.scheduleNotes}>📝 {session.notes}</Text>
        )}
      </View>
    );
  };

  const renderTrendIndicator = (trend: 'up' | 'down' | 'stable', value: string) => {
    const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';
    const trendColor = trend === 'up' ? Colors.teacher.success : trend === 'down' ? Colors.error[500] : Colors.text.secondary;
    
    return (
      <View style={styles.trendContainer}>
        <Text style={[styles.trendIcon, { color: trendColor }]}>{trendIcon}</Text>
        <Text style={[styles.trendValue, { color: trendColor }]}>{value}</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Enhanced Header */}
        <View style={styles.header}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>Good morning,</Text>
            <Text style={styles.teacherName}>Ms. Johnson</Text>
            <Text style={styles.classInfo}>Grade 3A • {studentCount} Students</Text>
          </View>
          <TouchableOpacity style={styles.avatarContainer}>
            <Text style={styles.avatarText}>👩‍🏫</Text>
            <View style={styles.statusIndicator} />
          </TouchableOpacity>
        </View>



        {/* Quick Actions - Moved to Top */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {quickActionCards.map((action) => (
              <TouchableOpacity key={action.id} style={styles.quickActionCard} onPress={action.onPress}>
                <Card variant="elevated" style={[styles.quickActionContent, { borderLeftColor: action.color }]}>
                  <View style={styles.quickActionHeader}>
                    <View style={[styles.quickActionIcon, { backgroundColor: action.color + '15' }]}>
                      <Text style={styles.quickActionIconText}>{action.icon}</Text>
                    </View>
                    {action.badge && (
                      <View style={[styles.quickActionBadge, { backgroundColor: action.color }]}>
                        <Text style={styles.quickActionBadgeText}>{action.badge}</Text>
                      </View>
                    )}
                  </View>
                  <View style={styles.quickActionTextContainer}>
                    <Text style={styles.quickActionTitle}>{action.title}</Text>
                    <Text style={styles.quickActionSubtitle}>{action.subtitle}</Text>
                  </View>
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Today's Teaching Schedule - Compact Single Card */}
        <Card style={styles.scheduleCard}>
          <View style={styles.scheduleCardHeader}>
            <Text style={styles.sectionTitle}>📅 Today's Teaching Schedule</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View Week</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.scheduleDate}>Monday, June 28, 2024</Text>
          <View style={styles.scheduleList}>
            {todaySchedule.map(renderTeachingSession)}
          </View>
        </Card>



        {/* Main Management Tasks - Card Pattern */}
        <View style={styles.managementTasksSection}>
          <Text style={styles.sectionTitle}>Main Management Tasks</Text>
          <View style={styles.managementTasksGrid}>
            {dashboardCards.map((card) => (
              <TouchableOpacity key={card.id} style={styles.managementTaskCard} onPress={card.onPress}>
                <Card variant="elevated" style={[styles.managementTaskContent, { borderLeftColor: card.color }]}>
                  <View style={styles.managementTaskHeader}>
                    <View style={[styles.managementTaskIcon, { backgroundColor: card.color + '15' }]}>
                      <Text style={styles.managementTaskIconText}>{card.icon}</Text>
                    </View>
                    {card.badge && (
                      <View style={[styles.managementTaskBadge, { backgroundColor: card.color }]}>
                        <Text style={styles.managementTaskBadgeText}>{card.badge}</Text>
                      </View>
                    )}
                  </View>
                  <View style={styles.managementTaskTextContainer}>
                    <Text style={styles.managementTaskTitle}>{card.title}</Text>
                    <Text style={styles.managementTaskSubtitle}>{card.description}</Text>
                  </View>
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        </View>



        {/* Class Performance Summary */}
        <Card variant="teacher" style={styles.performanceCard}>
          <Text style={styles.sectionTitle}>Class Performance Summary</Text>
          <View style={styles.performanceGrid}>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Math</Text>
              {renderProgressBar(89, Colors.teacher.primary)}
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Reading</Text>
              {renderProgressBar(94, Colors.teacher.success)}
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Science</Text>
              {renderProgressBar(76, Colors.teacher.warning)}
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Writing</Text>
              {renderProgressBar(82, Colors.teacher.info)}
            </View>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  scrollContent: {
    padding: Layout.screenPadding,
    paddingBottom: Spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    paddingTop: Spacing.sm,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  teacherName: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
  },
  classInfo: {
    fontSize: Typography.fontSize.sm,
    color: Colors.teacher.primary,
    fontWeight: '600' as const,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.teacher.primary,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    elevation: 3,
    shadowColor: Colors.teacher.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  avatarText: {
    fontSize: 28,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.teacher.success,
    borderWidth: 2,
    borderColor: Colors.background.primary,
  },
  // Removed timeframe selector styles - no longer needed
  statsCard: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: (width - Layout.screenPadding * 2 - Spacing.lg * 3) / 2,
    marginBottom: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.md,
    elevation: 1,
    shadowColor: Colors.neutral[400],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  statIcon: {
    fontSize: 24,
  },
  statValue: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: '700' as const,
    marginBottom: Spacing.xs / 2,
  },
  statLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    fontWeight: '600' as const,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendIcon: {
    fontSize: 12,
    marginRight: Spacing.xs / 2,
  },
  trendValue: {
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    marginRight: Spacing.sm,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
    minWidth: 30,
  },
  scheduleCard: {
    marginBottom: Spacing.lg,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  scheduleCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    paddingRight: Spacing.xs,
  },
  scheduleDate: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
    fontWeight: '500' as const,
  },
  scheduleList: {
    gap: Spacing.md,
  },
  scheduleItem: {
    backgroundColor: Colors.background.secondary,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.teacher.primary,
  },
  scheduleTime: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  scheduleTimeText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '700' as const,
    color: Colors.text.primary,
  },
  scheduleStatus: {
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
    color: Colors.text.secondary,
  },
  scheduleSubject: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
  },
  scheduleGroup: {
    fontSize: Typography.fontSize.sm,
    color: Colors.teacher.primary,
    fontWeight: '600' as const,
    marginBottom: Spacing.xs,
  },
  scheduleTopic: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
    fontWeight: '500' as const,
  },
  scheduleNotes: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  announcementBadge: {
    backgroundColor: Colors.teacher.warning,
    borderRadius: BorderRadius.full,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  announcementText: {
    fontSize: 12,
  },
  quickActionsSection: {
    marginBottom: Spacing.lg,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: '48%',
    marginBottom: Spacing.md,
  },
  quickActionContent: {
    padding: Spacing.md,
    borderLeftWidth: 4,
    height: 110,
    justifyContent: 'space-between',
  },
  quickActionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  quickActionTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  quickActionIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickActionIconText: {
    fontSize: 20,
  },
  quickActionBadge: {
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  quickActionBadgeText: {
    color: Colors.text.inverse,
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
  },
  quickActionTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
  },
  quickActionSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  scheduleSection: {
    marginBottom: Spacing.lg,
  },
  scheduleSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  scheduleCardsRow: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  scheduleCardHalf: {
    flex: 1,
    padding: Spacing.md,
  },
  scheduleCardTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  scheduleOverview: {
    gap: Spacing.sm,
  },
  overviewStat: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
    fontWeight: '500' as const,
  },
  managementTasksSection: {
    marginBottom: Spacing.lg,
  },
  managementTasksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  managementTaskCard: {
    width: '48%',
    marginBottom: Spacing.md,
  },
  managementTaskContent: {
    padding: Spacing.md,
    borderLeftWidth: 4,
    height: 110,
    justifyContent: 'space-between',
  },
  managementTaskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  managementTaskTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  managementTaskIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  managementTaskIconText: {
    fontSize: 20,
  },
  managementTaskBadge: {
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  managementTaskBadgeText: {
    color: Colors.text.inverse,
    fontSize: Typography.fontSize.xs,
    fontWeight: '600' as const,
  },
  managementTaskTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs / 2,
  },
  managementTaskSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  quickActionsScroll: {
    marginHorizontal: -Spacing.xs,
  },
  // Removed old action styles - using new management task styles above
  cardContent: {
    borderLeftWidth: 4,
    position: 'relative',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardIcon: {
    fontSize: 28,
  },
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.full,
  },
  badgeText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.background.primary,
    fontWeight: '600' as const,
  },
  cardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: '700' as const,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  cardButton: {
    alignSelf: 'flex-start',
  },
  activityCard: {
    marginBottom: Spacing.lg,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  viewAllButton: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.teacher.primary,
    fontWeight: '600' as const,
  },
  activityList: {
    gap: Spacing.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  activityIconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },
  activityIcon: {
    fontSize: 18,
  },
  activityContent: {
    flex: 1,
  },
  activityTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs / 2,
  },
  activityTitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    fontWeight: '600' as const,
    flex: 1,
  },
  priorityIndicator: {
    width: 20,
    height: 20,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.error[500],
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.sm,
  },
  priorityText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.background.primary,
    fontWeight: '700' as const,
  },
  activityDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs / 2,
  },
  activityTime: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.tertiary,
  },
  activityArrow: {
    fontSize: 20,
    color: Colors.text.tertiary,
    marginLeft: Spacing.sm,
  },
  performanceCard: {
    marginBottom: Spacing.lg,
  },
  performanceGrid: {
    gap: Spacing.md,
  },
  performanceItem: {
    marginBottom: Spacing.sm,
  },
  performanceLabel: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    fontWeight: '600' as const,
    marginBottom: Spacing.sm,
  },
});

export default TeacherDashboardScreen;
