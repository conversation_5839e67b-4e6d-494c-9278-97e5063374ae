import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { Button, Card, Input } from '../components';
import { Colors, Typography, Spacing, Layout, BorderRadius } from '../constants/theme';
import { Student } from '../types/Student';
import { StudentService } from '../services/StudentService';
import { v4 as uuidv4 } from 'uuid';

const { width } = Dimensions.get('window');

type StudentProfileScreenProps = NativeStackScreenProps<RootStackParamList, 'StudentProfile'>;

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  grade: string;
  section: string;
  dateOfBirth: string;
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  subjects: string[];
  notes: string;
}

const GRADE_OPTIONS = ['KG', '1st', '2nd', '3rd', '4th', '5th'];
const SECTION_OPTIONS = ['A', 'B', 'C', 'D'];
const SUBJECT_OPTIONS = ['Mathematics', 'English', 'Science', 'Social Studies', 'Art', 'Physical Education'];

const StudentProfileScreen = ({ navigation, route }: StudentProfileScreenProps) => {
  const { mode, studentId } = route.params;
  const isEditing = mode === 'edit' || mode === 'create';
  const isCreating = mode === 'create';

  const [loading, setLoading] = useState(false);
  const [student, setStudent] = useState<Student | null>(null);
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    grade: 'KG',
    section: 'A',
    dateOfBirth: '',
    parentName: '',
    parentEmail: '',
    parentPhone: '',
    subjects: [],
    notes: '',
  });

  useEffect(() => {
    if (studentId && !isCreating) {
      loadStudent();
    }
  }, [studentId, isCreating]);

  const loadStudent = async () => {
    try {
      setLoading(true);
      const studentData = await StudentService.getStudentById(studentId!);
      if (studentData) {
        setStudent(studentData);
        setFormData({
          firstName: studentData.firstName,
          lastName: studentData.lastName,
          email: studentData.email || '',
          grade: studentData.grade,
          section: studentData.section,
          dateOfBirth: studentData.dateOfBirth || '',
          parentName: studentData.parentName || '',
          parentEmail: studentData.parentEmail || '',
          parentPhone: studentData.parentPhone || '',
          subjects: studentData.subjects,
          notes: studentData.notes || '',
        });
      } else {
        Alert.alert('Error', 'Student not found');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error loading student:', error);
      Alert.alert('Error', 'Failed to load student data');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      Alert.alert('Validation Error', 'First name and last name are required');
      return;
    }

    try {
      setLoading(true);
      
      const studentData: Student = {
        id: isCreating ? uuidv4() : student!.id,
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim() || undefined,
        grade: formData.grade,
        section: formData.section,
        dateOfBirth: formData.dateOfBirth || undefined,
        parentName: formData.parentName.trim() || undefined,
        parentEmail: formData.parentEmail.trim() || undefined,
        parentPhone: formData.parentPhone.trim() || undefined,
        subjects: formData.subjects,
        notes: formData.notes.trim() || undefined,
        enrollmentDate: isCreating ? new Date().toISOString() : student!.enrollmentDate,
        status: isCreating ? 'active' : student!.status,
        completedLessons: isCreating ? [] : student!.completedLessons,
        completedExercises: isCreating ? [] : student!.completedExercises,
        achievements: isCreating ? [] : student!.achievements,
        lastActivity: isCreating ? undefined : student!.lastActivity,
      };

      await StudentService.saveStudent(studentData);
      
      Alert.alert(
        'Success',
        `Student ${isCreating ? 'created' : 'updated'} successfully`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving student:', error);
      Alert.alert('Error', `Failed to ${isCreating ? 'create' : 'update'} student`);
    } finally {
      setLoading(false);
    }
  };

  const handleSubjectToggle = (subject: string) => {
    setFormData(prev => ({
      ...prev,
      subjects: prev.subjects.includes(subject)
        ? prev.subjects.filter(s => s !== subject)
        : [...prev.subjects, subject]
    }));
  };

  const renderFormField = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    placeholder?: string,
    multiline?: boolean
  ) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <Input
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        multiline={multiline}
        style={multiline ? styles.textArea : undefined}
        editable={isEditing}
      />
    </View>
  );

  const renderPickerField = (
    label: string,
    value: string,
    options: string[],
    onSelect: (value: string) => void
  ) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={styles.pickerContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            style={[
              styles.pickerOption,
              value === option && styles.pickerOptionSelected,
              !isEditing && styles.pickerOptionDisabled
            ]}
            onPress={() => isEditing && onSelect(option)}
            disabled={!isEditing}
          >
            <Text style={[
              styles.pickerOptionText,
              value === option && styles.pickerOptionTextSelected
            ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSubjectsField = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>Subjects</Text>
      <View style={styles.subjectsContainer}>
        {SUBJECT_OPTIONS.map((subject) => (
          <TouchableOpacity
            key={subject}
            style={[
              styles.subjectChip,
              formData.subjects.includes(subject) && styles.subjectChipSelected,
              !isEditing && styles.subjectChipDisabled
            ]}
            onPress={() => isEditing && handleSubjectToggle(subject)}
            disabled={!isEditing}
          >
            <Text style={[
              styles.subjectChipText,
              formData.subjects.includes(subject) && styles.subjectChipTextSelected
            ]}>
              {subject}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderViewModeInfo = () => {
    if (!student || isEditing) return null;

    return (
      <Card style={styles.infoCard}>
        <View style={styles.infoHeader}>
          <View style={styles.studentAvatar}>
            <Text style={styles.avatarText}>
              {student.firstName.charAt(0)}{student.lastName.charAt(0)}
            </Text>
          </View>
          <View style={styles.studentBasicInfo}>
            <Text style={styles.studentName}>
              {student.firstName} {student.lastName}
            </Text>
            <Text style={styles.studentGrade}>
              Grade {student.grade} • Section {student.section}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(student.status) }]}>
              <Text style={styles.statusText}>{getStatusText(student.status)}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{student.completedLessons.length}</Text>
            <Text style={styles.statLabel}>Lessons</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{student.completedExercises.length}</Text>
            <Text style={styles.statLabel}>Exercises</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{student.achievements.length}</Text>
            <Text style={styles.statLabel}>Achievements</Text>
          </View>
        </View>
      </Card>
    );
  };

  const getStatusColor = (status: Student['status']) => {
    switch (status) {
      case 'active': return Colors.teacher.success;
      case 'inactive': return Colors.teacher.warning;
      case 'graduated': return Colors.teacher.info;
      default: return Colors.text.secondary;
    }
  };

  const getStatusText = (status: Student['status']) => {
    switch (status) {
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      case 'graduated': return 'Graduated';
      default: return 'Unknown';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>
            {isCreating ? 'Add New Student' : isEditing ? 'Edit Student' : 'Student Profile'}
          </Text>
        </View>

        {renderViewModeInfo()}

        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          {renderFormField(
            'First Name *',
            formData.firstName,
            (text) => setFormData(prev => ({ ...prev, firstName: text })),
            'Enter first name'
          )}

          {renderFormField(
            'Last Name *',
            formData.lastName,
            (text) => setFormData(prev => ({ ...prev, lastName: text })),
            'Enter last name'
          )}

          {renderFormField(
            'Email',
            formData.email,
            (text) => setFormData(prev => ({ ...prev, email: text })),
            'Enter email address'
          )}

          {renderPickerField(
            'Grade',
            formData.grade,
            GRADE_OPTIONS,
            (value) => setFormData(prev => ({ ...prev, grade: value }))
          )}

          {renderPickerField(
            'Section',
            formData.section,
            SECTION_OPTIONS,
            (value) => setFormData(prev => ({ ...prev, section: value }))
          )}

          {renderFormField(
            'Date of Birth',
            formData.dateOfBirth,
            (text) => setFormData(prev => ({ ...prev, dateOfBirth: text })),
            'YYYY-MM-DD'
          )}
        </Card>

        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Parent Information</Text>
          
          {renderFormField(
            'Parent Name',
            formData.parentName,
            (text) => setFormData(prev => ({ ...prev, parentName: text })),
            'Enter parent/guardian name'
          )}

          {renderFormField(
            'Parent Email',
            formData.parentEmail,
            (text) => setFormData(prev => ({ ...prev, parentEmail: text })),
            'Enter parent email'
          )}

          {renderFormField(
            'Parent Phone',
            formData.parentPhone,
            (text) => setFormData(prev => ({ ...prev, parentPhone: text })),
            'Enter phone number'
          )}
        </Card>

        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Academic Information</Text>
          
          {renderSubjectsField()}

          {renderFormField(
            'Notes',
            formData.notes,
            (text) => setFormData(prev => ({ ...prev, notes: text })),
            'Additional notes about the student...',
            true
          )}
        </Card>

        {isEditing && (
          <View style={styles.actionButtons}>
            <Button
              title="Cancel"
              onPress={() => navigation.goBack()}
              variant="ghost"
              style={styles.cancelButton}
            />
            <Button
              title={isCreating ? 'Create Student' : 'Save Changes'}
              onPress={handleSave}
              variant="teacher"
              style={styles.saveButton}
              loading={loading}
            />
          </View>
        )}

        {!isEditing && (
          <View style={styles.actionButtons}>
            <Button
              title="Edit Student"
              onPress={() => navigation.setParams({ mode: 'edit' })}
              variant="teacher"
              style={styles.editButton}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.teacher.background.primary,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.lg,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
  },
  infoCard: {
    marginBottom: Spacing.lg,
    padding: Spacing.lg,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  studentAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.teacher.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  avatarText: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.bold,
  },
  studentBasicInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  studentGrade: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.bold,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.teacher.border,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.teacher.primary,
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  formCard: {
    marginBottom: Spacing.lg,
    padding: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  fieldContainer: {
    marginBottom: Spacing.md,
  },
  fieldLabel: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  pickerOption: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.teacher.background.secondary,
    borderWidth: 1,
    borderColor: Colors.teacher.border,
  },
  pickerOptionSelected: {
    backgroundColor: Colors.teacher.primary,
    borderColor: Colors.teacher.primary,
  },
  pickerOptionDisabled: {
    opacity: 0.6,
  },
  pickerOptionText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
  },
  pickerOptionTextSelected: {
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.bold,
  },
  subjectsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  subjectChip: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.teacher.background.secondary,
    borderWidth: 1,
    borderColor: Colors.teacher.border,
  },
  subjectChipSelected: {
    backgroundColor: Colors.teacher.accent,
    borderColor: Colors.teacher.accent,
  },
  subjectChipDisabled: {
    opacity: 0.6,
  },
  subjectChipText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
  },
  subjectChipTextSelected: {
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.bold,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.lg,
    gap: Spacing.md,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 2,
  },
  editButton: {
    flex: 1,
  },
});

export default StudentProfileScreen;
