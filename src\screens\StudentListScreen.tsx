import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Dimensions
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { <PERSON><PERSON>, Card } from '../components';
import { Colors, Typography, Spacing, Layout, BorderRadius } from '../constants/theme';
import { Student, StudentFilters, StudentSortField, SortDirection } from '../types/Student';
import { StudentService } from '../services/StudentService';

const { width } = Dimensions.get('window');

type StudentListScreenProps = NativeStackScreenProps<RootStackParamList, 'StudentList'>;

interface StudentCardProps {
  student: Student;
  onPress: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

const StudentCard: React.FC<StudentCardProps> = ({ student, onPress, onEdit, onDelete }) => {
  const getStatusColor = (status: Student['status']) => {
    switch (status) {
      case 'active': return Colors.teacher.success;
      case 'inactive': return Colors.teacher.warning;
      case 'graduated': return Colors.teacher.info;
      default: return Colors.text.secondary;
    }
  };

  const getStatusText = (status: Student['status']) => {
    switch (status) {
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      case 'graduated': return 'Graduated';
      default: return 'Unknown';
    }
  };

  return (
    <Card style={styles.studentCard}>
      <TouchableOpacity onPress={onPress} style={styles.studentCardContent}>
        <View style={styles.studentHeader}>
          <View style={styles.studentAvatar}>
            <Text style={styles.avatarText}>
              {student.firstName.charAt(0)}{student.lastName.charAt(0)}
            </Text>
          </View>
          <View style={styles.studentInfo}>
            <Text style={styles.studentName}>
              {student.firstName} {student.lastName}
            </Text>
            <Text style={styles.studentDetails}>
              Grade {student.grade} • Section {student.section}
            </Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(student.status) }]}>
                <Text style={styles.statusText}>{getStatusText(student.status)}</Text>
              </View>
              {student.subjects.length > 0 && (
                <Text style={styles.subjectsText}>
                  {student.subjects.slice(0, 2).join(', ')}
                  {student.subjects.length > 2 && ` +${student.subjects.length - 2}`}
                </Text>
              )}
            </View>
          </View>
        </View>
        
        <View style={styles.studentActions}>
          <TouchableOpacity onPress={onEdit} style={styles.actionButton}>
            <Text style={styles.actionButtonText}>✏️</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={onDelete} style={[styles.actionButton, styles.deleteButton]}>
            <Text style={styles.actionButtonText}>🗑️</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Card>
  );
};

const StudentListScreen = ({ navigation }: StudentListScreenProps) => {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<StudentFilters>({});
  const [sortField, setSortField] = useState<StudentSortField>('firstName');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const loadStudents = useCallback(async () => {
    try {
      setLoading(true);
      const allStudents = await StudentService.getAllStudents();
      setStudents(allStudents);
      
      // Apply current filters and search
      const filtered = await StudentService.getFilteredStudents(
        { ...filters, search: searchQuery },
        sortField,
        sortDirection
      );
      setFilteredStudents(filtered);
    } catch (error) {
      console.error('Error loading students:', error);
      Alert.alert('Error', 'Failed to load students. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [filters, searchQuery, sortField, sortDirection]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadStudents();
    setRefreshing(false);
  }, [loadStudents]);

  useEffect(() => {
    loadStudents();
  }, [loadStudents]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadStudents();
    });
    return unsubscribe;
  }, [navigation, loadStudents]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    const filtered = await StudentService.getFilteredStudents(
      { ...filters, search: query },
      sortField,
      sortDirection
    );
    setFilteredStudents(filtered);
  };

  const handleAddStudent = () => {
    navigation.navigate('StudentProfile', { mode: 'create' });
  };

  const handleEditStudent = (student: Student) => {
    navigation.navigate('StudentProfile', { mode: 'edit', studentId: student.id });
  };

  const handleViewStudent = (student: Student) => {
    navigation.navigate('StudentProfile', { mode: 'view', studentId: student.id });
  };

  const handleDeleteStudent = (student: Student) => {
    Alert.alert(
      'Delete Student',
      `Are you sure you want to delete ${student.firstName} ${student.lastName}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await StudentService.deleteStudent(student.id);
              await loadStudents();
              Alert.alert('Success', 'Student deleted successfully.');
            } catch (error) {
              console.error('Error deleting student:', error);
              Alert.alert('Error', 'Failed to delete student. Please try again.');
            }
          }
        }
      ]
    );
  };

  const renderStudentItem = ({ item }: { item: Student }) => (
    <StudentCard
      student={item}
      onPress={() => handleViewStudent(item)}
      onEdit={() => handleEditStudent(item)}
      onDelete={() => handleDeleteStudent(item)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.titleSection}>
        <Text style={styles.title}>Student Roster</Text>
        <Text style={styles.subtitle}>
          {filteredStudents.length} of {students.length} students
        </Text>
      </View>
      
      <View style={styles.searchSection}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search students..."
          value={searchQuery}
          onChangeText={handleSearch}
          placeholderTextColor={Colors.text.secondary}
        />
      </View>

      <View style={styles.actionSection}>
        <Button
          title="+ Add Student"
          onPress={handleAddStudent}
          variant="teacher"
          style={styles.addButton}
        />
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>👥</Text>
      <Text style={styles.emptyStateTitle}>No Students Found</Text>
      <Text style={styles.emptyStateText}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Try adjusting your search or filters'
          : 'Get started by adding your first student'}
      </Text>
      {!searchQuery && Object.keys(filters).length === 0 && (
        <Button
          title="Add First Student"
          onPress={handleAddStudent}
          variant="teacher"
          style={styles.emptyStateButton}
        />
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={filteredStudents}
        renderItem={renderStudentItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.teacher.primary]}
            tintColor={Colors.teacher.primary}
          />
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.teacher.background.primary,
  },
  listContent: {
    flexGrow: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.lg,
  },
  titleSection: {
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  searchSection: {
    marginBottom: Spacing.md,
  },
  searchInput: {
    ...Typography.body.medium,
    backgroundColor: Colors.teacher.background.secondary,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.neutral[300],
    color: Colors.text.primary,
  },
  actionSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  addButton: {
    paddingHorizontal: Spacing.lg,
  },
  studentCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  studentCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  studentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  studentAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.teacher.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  avatarText: {
    ...Typography.body.medium,
    color: Colors.common.white,
    fontWeight: 'bold',
  },
  studentInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.primary,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.xs,
  },
  studentDetails: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.sm,
  },
  statusText: {
    ...Typography.body.small,
    color: Colors.text.inverse,
    fontWeight: 'bold',
  },
  subjectsText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  studentActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.teacher.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  deleteButton: {
    backgroundColor: Colors.teacher.error + '20',
  },
  actionButtonText: {
    fontSize: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.xl * 2,
  },
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: Spacing.lg,
  },
  emptyStateTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.lg,
  },
  emptyStateButton: {
    paddingHorizontal: Spacing.xl,
  },
});

export default StudentListScreen;
