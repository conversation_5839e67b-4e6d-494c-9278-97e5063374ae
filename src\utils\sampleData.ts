import { Student } from '../types/Student';
import { v4 as uuidv4 } from 'uuid';

export const generateSampleStudents = (): Student[] => {
  const sampleStudents: Student[] = [
    {
      id: uuidv4(),
      firstName: 'Emma',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      grade: '3rd',
      section: 'A',
      dateOfBirth: '2015-03-15',
      parentName: '<PERSON>',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0101',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Science'],
      notes: 'Excellent student with strong reading skills.',
      completedLessons: ['lesson1', 'lesson2', 'lesson3'],
      completedExercises: ['exercise1', 'exercise2'],
      achievements: ['First Week Complete', 'Math Star'],
      lastActivity: '2024-06-27T10:30:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: '<PERSON>',
      lastName: 'Smith',
      email: '<EMAIL>',
      grade: '2nd',
      section: 'B',
      dateOfBirth: '2016-07-22',
      parentName: '<PERSON>',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0102',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Art'],
      notes: 'Creative student who loves art projects.',
      completedLessons: ['lesson1', 'lesson2'],
      completedExercises: ['exercise1'],
      achievements: ['Creative Thinker'],
      lastActivity: '2024-06-26T14:15:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'Olivia',
      lastName: 'Brown',
      email: '<EMAIL>',
      grade: '4th',
      section: 'A',
      dateOfBirth: '2014-11-08',
      parentName: 'Jennifer Brown',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0103',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Science', 'Social Studies'],
      notes: 'Advanced student, ready for challenging material.',
      completedLessons: ['lesson1', 'lesson2', 'lesson3', 'lesson4'],
      completedExercises: ['exercise1', 'exercise2', 'exercise3'],
      achievements: ['Honor Roll', 'Science Fair Winner', 'Reading Champion'],
      lastActivity: '2024-06-28T09:45:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'Noah',
      lastName: 'Davis',
      email: '<EMAIL>',
      grade: '1st',
      section: 'C',
      dateOfBirth: '2017-01-30',
      parentName: 'David Davis',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0104',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English'],
      notes: 'Enthusiastic learner, needs encouragement with reading.',
      completedLessons: ['lesson1'],
      completedExercises: [],
      achievements: ['First Day Champion'],
      lastActivity: '2024-06-25T11:20:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'Ava',
      lastName: 'Wilson',
      email: '<EMAIL>',
      grade: '5th',
      section: 'A',
      dateOfBirth: '2013-09-12',
      parentName: 'Lisa Wilson',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0105',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Science', 'Social Studies', 'Physical Education'],
      notes: 'Natural leader, helps other students.',
      completedLessons: ['lesson1', 'lesson2', 'lesson3', 'lesson4', 'lesson5'],
      completedExercises: ['exercise1', 'exercise2', 'exercise3', 'exercise4'],
      achievements: ['Leadership Award', 'Perfect Attendance', 'Math Wizard', 'Team Player'],
      lastActivity: '2024-06-28T13:10:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'William',
      lastName: 'Miller',
      email: '<EMAIL>',
      grade: 'KG',
      section: 'B',
      dateOfBirth: '2018-05-18',
      parentName: 'Amanda Miller',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0106',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English'],
      notes: 'Loves alphabet games and counting activities.',
      completedLessons: [],
      completedExercises: [],
      achievements: [],
      lastActivity: '2024-06-24T10:00:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'Sophia',
      lastName: 'Garcia',
      email: '<EMAIL>',
      grade: '3rd',
      section: 'B',
      dateOfBirth: '2015-12-03',
      parentName: 'Maria Garcia',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0107',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Science', 'Art'],
      notes: 'Bilingual student, excels in both languages.',
      completedLessons: ['lesson1', 'lesson2'],
      completedExercises: ['exercise1', 'exercise2'],
      achievements: ['Bilingual Star', 'Art Excellence'],
      lastActivity: '2024-06-27T15:30:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'James',
      lastName: 'Anderson',
      email: '<EMAIL>',
      grade: '2nd',
      section: 'A',
      dateOfBirth: '2016-04-25',
      parentName: 'Robert Anderson',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0108',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Physical Education'],
      notes: 'Very active student, learns best through movement.',
      completedLessons: ['lesson1'],
      completedExercises: ['exercise1'],
      achievements: ['Sports Star'],
      lastActivity: '2024-06-26T12:45:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'Isabella',
      lastName: 'Taylor',
      email: '<EMAIL>',
      grade: '4th',
      section: 'C',
      dateOfBirth: '2014-08-14',
      parentName: 'Michelle Taylor',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0109',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English', 'Science', 'Social Studies'],
      notes: 'Curious about science, asks great questions.',
      completedLessons: ['lesson1', 'lesson2', 'lesson3'],
      completedExercises: ['exercise1', 'exercise2'],
      achievements: ['Science Explorer', 'Question Master'],
      lastActivity: '2024-06-27T16:20:00.000Z',
    },
    {
      id: uuidv4(),
      firstName: 'Benjamin',
      lastName: 'Thomas',
      email: '<EMAIL>',
      grade: '1st',
      section: 'A',
      dateOfBirth: '2017-06-09',
      parentName: 'Karen Thomas',
      parentEmail: '<EMAIL>',
      parentPhone: '******-0110',
      enrollmentDate: '2023-08-15T00:00:00.000Z',
      status: 'active',
      subjects: ['Mathematics', 'English'],
      notes: 'Quiet student who works well independently.',
      completedLessons: ['lesson1'],
      completedExercises: [],
      achievements: ['Independent Learner'],
      lastActivity: '2024-06-25T14:00:00.000Z',
    },
  ];

  return sampleStudents;
};

export const initializeSampleData = async () => {
  try {
    const { StudentService } = await import('../services/StudentService');
    const existingStudents = await StudentService.getAllStudents();
    
    if (existingStudents.length === 0) {
      const sampleStudents = generateSampleStudents();
      
      for (const student of sampleStudents) {
        await StudentService.saveStudent(student);
      }
      
      console.log('Sample student data initialized successfully');
      return true;
    }
    
    console.log('Student data already exists, skipping initialization');
    return false;
  } catch (error) {
    console.error('Error initializing sample data:', error);
    return false;
  }
};
